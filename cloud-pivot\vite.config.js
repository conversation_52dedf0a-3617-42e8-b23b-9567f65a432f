// vite.config.js
import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import path from 'path';

export default defineConfig({
  plugins: [vue()],
  server: {
    host: 'localhost',
    port: 5173
  },
  
  // 配置别名
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src')
    }
  },
  
  // 构建配置
  build: {
    // 确保静态资源放在assets目录
    assetsDir: 'assets',
    
    // 多入口配置
    rollupOptions: {
      input: {
        main: path.resolve(__dirname, './index.html'),
        login: path.resolve(__dirname, './login.html'),
        back: path.resolve(__dirname, './back.html'),
        address_book: path.resolve(__dirname, './address_book.html'),
        address_book_management: path.resolve(__dirname, './address_book_management.html')
      }
    }
  }

  // 其他配置...
});
<template>
  <div class="address-book-management-container">
    <!-- 左侧部门表格 -->
    <div class="left-panel">
      <el-input
        v-model="searchDepartment"
        placeholder="搜索单位"
        class="department-search"
        clearable
      />
      <el-table
        :data="filteredDepartmentList"
        border
        stripe
        fit
        class="department-table"
        @row-click="handleDepartmentClick"
      >
        <el-table-column prop="unit_name" label="单位名称">
          <template #default="{ row }">
            <span class="indent" :style="{ width: `${row.indent}px` }"></span>
            <span @click="handleDepartmentNameClick(row)" style="cursor: pointer;">
              {{ row.unit_name }}
            </span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 右侧用户管理 -->
    <div class="right-panel">


      <!-- 操作按钮区域 -->
      <div class="action-buttons" style="display: flex; align-items: center; margin-bottom: 10px;">
        <!-- 左侧搜索框 -->
        <div class="left-search" style="display: flex; align-items: center;">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索用户（姓名、身份证号、电话号码）"
            clearable
            style="width: 300px;"
            @input="handleSearch"
            @clear="handleSearchClear"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>

        <!-- 右侧信息显示 -->
        <div class="right-info" style="margin-left: auto; display: flex; align-items: center; gap: 10px;">
          <!-- 显示当前选择的单位名称 -->
          <el-tag
            type="info"
            v-if="currentDepartmentName"
          >
            当前单位：{{ currentDepartmentName }}
          </el-tag>
        </div>
      </div>

      <!-- 用户列表 -->
      <el-table
        :data="userList"
        border
        stripe
        fit
        class="user-table"
        v-loading="loading"
      >
        <el-table-column
          type="index"
          label="序号"
          width="60"
          align="center"
          :index="(index) => (currentPage - 1) * pageSize + index + 1"
        />
        <el-table-column prop="name" label="姓名" width="120" align="center" />
        <el-table-column prop="id_number" label="身份证号" width="200" align="center" />
        <el-table-column prop="phone" label="电话I" align="center" />
        <el-table-column prop="alt_phone_1" label="电话II" align="center" />
        <el-table-column prop="alt_phone_2" label="电话III" align="center" />
        <el-table-column prop="short_code" label="短号" align="center" />
        <el-table-column prop="landline" label="座机" align="center" />
        
        <el-table-column label="操作" width="120" align="center">
          <template #default="{ row }">
            <el-button 
              type="primary" 
              size="small" 
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <el-pagination
        :current-page="currentPage"
        :page-size="pageSize"
        :total="total"
        layout="prev, pager, next, jumper, sizes"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      />
    </div>

    <!-- 编辑用户对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="编辑用户联系方式"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        :model="userForm"
        ref="userFormRef"
        label-width="120px"
      >
        <el-form-item label="姓名" prop="name">
          <el-input v-model="userForm.name" disabled />
        </el-form-item>
        <el-form-item label="身份证号" prop="id_number">
          <el-input v-model="userForm.id_number" disabled />
        </el-form-item>
        <el-form-item label="电话I" prop="phone">
          <el-input v-model="userForm.phone" maxlength="11" />
        </el-form-item>
        <el-form-item label="电话II" prop="alt_phone_1">
          <el-input v-model="userForm.alt_phone_1" maxlength="11" />
        </el-form-item>
        <el-form-item label="电话III" prop="alt_phone_2">
          <el-input v-model="userForm.alt_phone_2" maxlength="11" />
        </el-form-item>
        <el-form-item label="短号" prop="short_code">
          <el-input v-model="userForm.short_code" />
        </el-form-item>
        <el-form-item label="座机" prop="landline">
          <el-input v-model="userForm.landline" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import request from '@/utils/requests'

// 响应式数据
const loading = ref(false)
const dialogVisible = ref(false)
const selectedDepartmentId = ref(null)
const searchDepartment = ref('')
const searchKeyword = ref('')

// 分页相关
const currentPage = ref(1)
const pageSize = ref(50)
const total = ref(0)

// 数据存储
const departmentTree = ref([])
const flatDepartmentList = ref([])
const userList = ref([])

// 表单相关
const userFormRef = ref(null)
const userForm = reactive({
  id: '',
  name: '',
  id_number: '',
  phone: '',
  alt_phone_1: '',
  alt_phone_2: '',
  short_code: '',
  landline: ''
})





// 过滤后的部门列表
const filteredDepartmentList = computed(() => {
  if (!searchDepartment.value) {
    return flatDepartmentList.value.filter(item => item.show !== false)
  }
  return flatDepartmentList.value.filter(item =>
    (item.show !== false) && item.unit_name.toLowerCase().includes(searchDepartment.value.toLowerCase())
  )
})

// 方法定义 - 扁平化树形数据
const flattenTree = (items, indent = 0) => {
  items.forEach(item => {
    item.indent = indent
    item.show = true
    flatDepartmentList.value.push(item)

    if (item.children && item.children.length > 0) {
      flattenTree(item.children, indent + 20)
    }
  })
}

// 计算属性
const currentDepartmentName = computed(() => {
  if (!selectedDepartmentId.value) return ''
  const targetDepartment = flatDepartmentList.value.find(dep => dep.id === selectedDepartmentId.value)
  return targetDepartment ? targetDepartment.unit_name : ''
})

// 获取单位信息
const fetchUnits = async () => {
  try {
    console.log('开始获取单位信息...')
    loading.value = true
    const formData = new FormData()
    formData.append('controlCode', 'get_units')

    console.log('发送请求到: /api/address_book_management.php')
    console.log('请求参数:', { controlCode: 'get_units' })

    const response = await request.post('/api/address_book_management.php', formData)
    console.log('接口响应:', response)

    if (response.status === 1) {
      departmentTree.value = response.data
      flatDepartmentList.value = []
      flattenTree(response.data)
      console.log('单位数据加载成功，共', flatDepartmentList.value.length, '个单位')
    } else {

      ElMessage.error(response.message || '获取单位信息失败')
    }
  } catch (error) {
    console.error('获取单位信息失败:', error)
  } finally {
    loading.value = false
  }
}

// 获取用户信息
const fetchUsers = async () => {
  try {
    loading.value = true
    const formData = new FormData()
    formData.append('controlCode', 'get_user')

    // 如果没有选择部门，获取所有有权限的单位的用户；否则获取指定部门的用户
    if (selectedDepartmentId.value) {
      formData.append('unitId', selectedDepartmentId.value)
    } else {
      // 不传unitId，让后端根据用户权限返回所有有权限的单位的用户
      formData.append('unitId', '')
    }

    formData.append('page', currentPage.value)
    formData.append('pageSize', pageSize.value)

    // 添加搜索关键词
    if (searchKeyword.value) {
      formData.append('search', searchKeyword.value)
    }

    const response = await request.post('/api/address_book_management.php', formData)
    if (response.status === 1) {
      userList.value = response.data
      total.value = response.pagination.totalCount
    } else {
      ElMessage.error(response.message || '获取用户信息失败')
      userList.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
    ElMessage.error('获取用户信息失败')
    userList.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 处理部门点击
const handleDepartmentClick = (row) => {
  selectedDepartmentId.value = row.id
  currentPage.value = 1
  fetchUsers()
}

const handleDepartmentNameClick = (row) => {
  handleDepartmentClick(row)
}

// 处理编辑 - 修复表单重置时机问题
const handleEdit = (row) => {
  // 先赋值数据
  Object.assign(userForm, {
    id: row.id,
    name: row.name || '',
    id_number: row.id_number || '',
    phone: row.phone || '',
    alt_phone_1: row.alt_phone_1 || '',
    alt_phone_2: row.alt_phone_2 || '',
    short_code: row.short_code || '',
    landline: row.landline || ''
  })

  // 再打开对话框
  dialogVisible.value = true

  // 最后重置表单验证状态（如果需要的话）
  if (userFormRef.value) {
    userFormRef.value.clearValidate()
  }
}



// 提交表单 - 完全按照UnitManagement.vue的模式
const submitForm = async () => {
  dialogVisible.value = false
  try {
    const formData = new FormData()
    formData.append('controlCode', 'edit_user')
    formData.append('id', userForm.id)
    formData.append('phone', userForm.phone)
    formData.append('alt_phone_1', userForm.alt_phone_1)
    formData.append('alt_phone_2', userForm.alt_phone_2)
    formData.append('short_code', userForm.short_code)
    formData.append('landline', userForm.landline)

    const response = await request.post('/api/address_book_management.php', formData)

    if (response.status === 1) {
      // 重新获取数据
      await fetchUsers()
      
      ElMessage.success('更新成功')
    } else {
      ElMessage.error(response.message)
    }
  } catch (error) {
    console.error('更新用户信息失败:', error)
    ElMessage.error('更新用户信息失败，请稍后重试')
  }
}



// 搜索处理
const handleSearch = () => {
  currentPage.value = 1 // 搜索时重置到第一页
  fetchUsers()
}

const handleSearchClear = () => {
  searchKeyword.value = ''
  currentPage.value = 1
  fetchUsers()
}

// 分页处理
const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchUsers()
}

const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  fetchUsers()
}

// 监听部门选择变化
watch(selectedDepartmentId, () => {
  if (selectedDepartmentId.value) {
    fetchUsers()
  }
})

// 页面初始化
onMounted(() => {
  console.log('AddressBookManagement页面已挂载，开始初始化...')
  fetchUnits()
  fetchUsers() // 初始加载所有用户
})
</script>

<style scoped>
/* 整体容器美化 */
.address-book-management-container {
  display: flex;
  height: calc(100vh - 120px);
  gap: 20px;
  padding: 20px;
  height: 100vh;
  width: 100vw;
  box-sizing: border-box;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  overflow: hidden;
}

/* 左侧面板美化 */
.left-panel {
  width: 300px;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
  backdrop-filter: blur(4px);
  border: 1px solid rgba(255, 255, 255, 0.18);
  padding: 20px;
  transition: all 0.3s ease;
}

.left-panel:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(31, 38, 135, 0.5);
}

/* 搜索框美化 */
.department-search {
  margin-bottom: 10px;
}

.department-search :deep(.el-input__wrapper) {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e8ed;
  transition: all 0.3s ease;
}

.department-search :deep(.el-input__wrapper:hover) {
  border-color: #667eea;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

/* 部门表格美化 */
.department-table {
  flex: 1;
  overflow: auto;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  margin-bottom: 10px;
}

.department-table :deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

.department-table :deep(.el-table__header) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.department-table :deep(.el-table__header th) {
  background: transparent;
  color: #333;
  font-weight: 600;
  border: none;
}

.department-table :deep(.el-table__body tr:hover) {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  color: #1565c0;
  font-weight: 600;
}

.department-table :deep(.el-table__body tr) {
  color: #333;
}

.department-table .cell {
  padding-left: 10px !important;
  font-weight: 500;
}

/* 右侧面板美化 */
.right-panel {
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  gap: 20px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
  backdrop-filter: blur(4px);
  border: 1px solid rgba(255, 255, 255, 0.18);
  padding: 20px;
  transition: all 0.3s ease;
}

.right-panel:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(31, 38, 135, 0.5);
}

/* 操作按钮区域美化 */
.action-buttons {
  flex: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
}

.action-buttons :deep(.el-tag) {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: #333;
  border-radius: 6px;
}

.right-info {
  display: flex;
  align-items: center;
}

/* 用户表格美化 */
.user-table {
  flex: 1;
  overflow-y: auto;
  position: relative;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.user-table :deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

.user-table :deep(.el-table__header) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.user-table :deep(.el-table__header th) {
  background: transparent;
  color: #333;
  font-weight: 600;
  border: none;
}

.user-table :deep(.el-table__body tr:hover) {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  color: #1565c0;
  font-weight: 600;
}

.user-table :deep(.el-table__body tr) {
  color: #333;
}

.user-table :deep(.el-table__body td) {
  font-weight: 500;
}

.indent {
  display: inline-block;
  width: var(--indent-width, 0px);
}

/* 分页美化 */
.el-pagination {
  flex: 0 0 auto;
  justify-content: center;
  margin-top: 5px;
  margin-bottom: 5px;
}

.el-pagination :deep(.el-pager li) {
  border-radius: 6px;
  margin: 0 2px;
  transition: all 0.3s ease;
}

.el-pagination :deep(.el-pager li:hover) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.el-pagination :deep(.el-pager li.is-active) {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}



.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 按钮美化 */
:deep(.el-button--primary) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 6px;
  padding: 10px 20px;
  font-weight: 600;
  transition: all 0.3s ease;
}

:deep(.el-button--primary:hover) {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(240, 147, 251, 0.4);
}

/* 表单美化 */
:deep(.el-form-item__label) {
  font-weight: 600;
  color: #333;
}

:deep(.el-input__wrapper) {
  border-radius: 6px;
  border: 1px solid #e1e8ed;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

:deep(.el-input__wrapper:hover) {
  border-color: #667eea;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
}

:deep(.el-input__wrapper.is-focus) {
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

/* 滚动条美化 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 4px;
  transition: all 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}



/* 对话框美化 - 不影响动画 */
:deep(.el-dialog) {
  border-radius: 12px;
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.2);
  overflow: hidden;
}

:deep(.el-dialog__header) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px 24px;
}

:deep(.el-dialog__title) {
  font-weight: 600;
  font-size: 18px;
}

:deep(.el-dialog__body) {
  padding: 20px 24px;
}

/* 动画效果 - 只作用于主要面板，不影响对话框 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 只给左右面板添加动画，不影响对话框 */
.left-panel {
  animation: fadeInUp 0.6s ease-out;
  animation-delay: 0.1s;
}

.right-panel {
  animation: fadeInUp 0.6s ease-out;
  animation-delay: 0.2s;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .address-book-management-container {
    flex-direction: column;
    height: auto;
  }

  .left-panel {
    width: 100%;
    height: 300px;
  }

  .action-buttons {
    justify-content: center;
  }

  .right-info {
    justify-content: center;
  }
}
</style>
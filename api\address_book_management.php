<?php
require_once '../conn_waf.php';
//引入waf并检测输入参数是否有注入风险

// 设置响应头为JSON格式
header('Content-Type: application/json; charset=utf-8');

// 检查session是否过期（30分钟无活动）
if (isset($_SESSION['last_activity']) && (time() - $_SESSION['last_activity'] > 1800)) {
    session_unset();
    session_destroy();
    echo json_encode(['status' => 0, 'message' => '未登录或登录信息已过期']);
    exit;
}

// 检查用户是否登录
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['status' => 0, 'message' => '未登录或登录信息已过期']);
    exit;
}

// 更新最后活动时间
$_SESSION['last_activity'] = time();

// 构建树状结构函数
function buildTree(array $items, $parentId = null) {
    $branch = array();

    foreach ($items as $item) {
        if ($item['parent_id'] == $parentId) {
            $children = buildTree($items, $item['id']);
            if ($children) {
                // 对子节点按sort_order排序
                usort($children, function($a, $b) {
                    return $a['sort_order'] - $b['sort_order'];
                });
                $item['children'] = $children;
            }
            $branch[] = $item;
        }
    }

    // 对当前层级节点按sort_order排序
    usort($branch, function($a, $b) {
        return $a['sort_order'] - $b['sort_order'];
    });

    return $branch;
}

// 获取所有子单位的递归函数
function getAllChildUnits($conn, $parentId, &$unitIds) {
    $sql = "SELECT id FROM 2_unit WHERE parent_id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('i', $parentId);
    $stmt->execute();
    $result = $stmt->get_result();

    while ($row = $result->fetch_assoc()) {
        $unitIds[] = $row['id'];
        // 递归获取子单位
        getAllChildUnits($conn, $row['id'], $unitIds);
    }
}

// 获取用户信息的函数
function getUsers() {
    global $conn;

    $unitId = $_POST['unitId'] ?? $_GET['unitId'] ?? '';
    $page = $_POST['page'] ?? $_GET['page'] ?? 1;
    $pageSize = $_POST['pageSize'] ?? $_GET['pageSize'] ?? 10;
    $search = $_POST['search'] ?? $_GET['search'] ?? '';

    // 如果没有传递unitId，获取用户有权限的所有单位的用户
    $getAllAuthorizedUsers = empty($unitId);

    if (!$getAllAuthorizedUsers && !is_numeric($unitId)) {
        return [
            'status' => 0,
            'message' => '单位ID参数无效'
        ];
    }

    // 验证分页参数
    $page = max(1, intval($page));
    $pageSize = max(1, min(100, intval($pageSize))); // 限制最大每页100条
    $offset = ($page - 1) * $pageSize;

    try {
        if ($getAllAuthorizedUsers) {
            // 获取用户有权限的所有单位的用户
            // 首先获取用户有权限的所有单位ID
            $userId = $_SESSION['user_id'];

            // 查询用户角色信息
            $userRoleSql = "SELECT roleId, unitId, appId FROM 3_user_Role WHERE userId = ?";
            $userRoleStmt = $conn->prepare($userRoleSql);
            $userRoleStmt->bind_param('i', $userId);
            $userRoleStmt->execute();
            $userRoleResult = $userRoleStmt->get_result();

            if ($userRoleResult->num_rows === 0) {
                return [
                    'status' => 0,
                    'message' => '权限不足'
                ];
            }

            // 获取用户所有角色
            $userRoles = [];
            while ($roleRow = $userRoleResult->fetch_assoc()) {
                $userRoles[] = $roleRow;
            }

            // 检查是否是系统管理员 (roleId = 1)
            $isSystemAdmin = false;
            $authorizedUnits = [];

            foreach ($userRoles as $role) {
                if ($role['roleId'] == 1) {
                    $isSystemAdmin = true;
                    break;
                }
                // 收集部门管理员(3)和电话方式编辑人员(12)的授权单位
                if ($role['roleId'] == 3 || $role['roleId'] == 12) {
                    $authorizedUnits[] = $role['unitId'];
                }
            }

            if ($isSystemAdmin) {
                // 系统管理员：查询所有用户
                $searchCondition = '';
                $searchParams = [];
                $searchTypes = '';

                if (!empty($search)) {
                    $searchCondition = " WHERE name LIKE ? OR id_number LIKE ? OR phone LIKE ? OR alt_phone_1 LIKE ? OR alt_phone_2 LIKE ?";
                    $searchValue = '%' . $search . '%';
                    $searchParams = [$searchValue, $searchValue, $searchValue, $searchValue, $searchValue];
                    $searchTypes = 'sssss';
                }

                $countSql = "SELECT COUNT(*) as total FROM 3_user" . $searchCondition;
                $countStmt = $conn->prepare($countSql);

                if (!empty($search)) {
                    $countStmt->bind_param($searchTypes, ...$searchParams);
                }

                if (!$countStmt->execute()) {
                    return [
                        'status' => 0,
                        'message' => '查询用户总数失败'
                    ];
                }

                $countResult = $countStmt->get_result();
                $totalCount = $countResult->fetch_assoc()['total'];

                $userSql = "SELECT id, name, id_number, phone, alt_phone_1, alt_phone_2, short_code, landline
                           FROM 3_user" . $searchCondition . "
                           ORDER BY id ASC
                           LIMIT ? OFFSET ?";
                $userStmt = $conn->prepare($userSql);

                if (!empty($search)) {
                    $allParams = array_merge($searchParams, [$pageSize, $offset]);
                    $allTypes = $searchTypes . 'ii';
                    $userStmt->bind_param($allTypes, ...$allParams);
                } else {
                    $userStmt->bind_param('ii', $pageSize, $offset);
                }

            } else if (!empty($authorizedUnits)) {
                // 部门管理员或电话方式编辑人员：获取授权单位及其子单位的用户
                $allAuthorizedUnits = $authorizedUnits;

                // 获取所有授权单位的子单位
                foreach ($authorizedUnits as $unitId) {
                    getAllChildUnits($conn, $unitId, $allAuthorizedUnits);
                }

                // 去重
                $allAuthorizedUnits = array_unique($allAuthorizedUnits);

                if (!empty($allAuthorizedUnits)) {
                    $placeholders = implode(',', array_fill(0, count($allAuthorizedUnits), '?'));

                    // 构建搜索条件
                    $searchCondition = '';
                    $searchParams = [];
                    $searchTypes = '';

                    if (!empty($search)) {
                        $searchCondition = " AND (name LIKE ? OR id_number LIKE ? OR phone LIKE ? OR alt_phone_1 LIKE ? OR alt_phone_2 LIKE ?)";
                        $searchValue = '%' . $search . '%';
                        $searchParams = [$searchValue, $searchValue, $searchValue, $searchValue, $searchValue];
                        $searchTypes = 'sssss';
                    }

                    // 先查询总数
                    $countSql = "SELECT COUNT(*) as total FROM 3_user WHERE organization_unit IN ($placeholders)" . $searchCondition;
                    $countStmt = $conn->prepare($countSql);

                    $countParams = $allAuthorizedUnits;
                    $countTypes = str_repeat('i', count($allAuthorizedUnits));

                    if (!empty($search)) {
                        $countParams = array_merge($countParams, $searchParams);
                        $countTypes .= $searchTypes;
                    }

                    $countStmt->bind_param($countTypes, ...$countParams);

                    if (!$countStmt->execute()) {
                        return [
                            'status' => 0,
                            'message' => '查询用户总数失败'
                        ];
                    }

                    $countResult = $countStmt->get_result();
                    $totalCount = $countResult->fetch_assoc()['total'];

                    // 查询分页数据
                    $userSql = "SELECT id, name, id_number, phone, alt_phone_1, alt_phone_2, short_code, landline
                               FROM 3_user
                               WHERE organization_unit IN ($placeholders)" . $searchCondition . "
                               ORDER BY id ASC
                               LIMIT ? OFFSET ?";
                    $userStmt = $conn->prepare($userSql);

                    // 绑定参数：单位IDs + 搜索参数 + LIMIT + OFFSET
                    $allParams = $allAuthorizedUnits;
                    $allTypes = str_repeat('i', count($allAuthorizedUnits));

                    if (!empty($search)) {
                        $allParams = array_merge($allParams, $searchParams);
                        $allTypes .= $searchTypes;
                    }

                    $allParams = array_merge($allParams, [$pageSize, $offset]);
                    $allTypes .= 'ii';

                    $userStmt->bind_param($allTypes, ...$allParams);
                } else {
                    return [
                        'status' => 0,
                        'message' => '用户没有授权的单位'
                    ];
                }
            } else {
                return [
                    'status' => 0,
                    'message' => '权限不足'
                ];
            }

        } else {
            // 获取该单位及其所有子单位的ID
            $allUnitIds = [$unitId];
            getAllChildUnits($conn, $unitId, $allUnitIds);

            if (!empty($allUnitIds)) {
                $placeholders = implode(',', array_fill(0, count($allUnitIds), '?'));

                // 构建搜索条件
                $searchCondition = '';
                $searchParams = [];
                $searchTypes = '';

                if (!empty($search)) {
                    $searchCondition = " AND (name LIKE ? OR id_number LIKE ? OR phone LIKE ? OR alt_phone_1 LIKE ? OR alt_phone_2 LIKE ?)";
                    $searchValue = '%' . $search . '%';
                    $searchParams = [$searchValue, $searchValue, $searchValue, $searchValue, $searchValue];
                    $searchTypes = 'sssss';
                }

                // 先查询总数
                $countSql = "SELECT COUNT(*) as total FROM 3_user WHERE organization_unit IN ($placeholders)" . $searchCondition;
                $countStmt = $conn->prepare($countSql);

                if (!$countStmt) {
                    return [
                        'status' => 0,
                        'message' => '数据库准备失败: ' . $conn->error
                    ];
                }

                $countParams = $allUnitIds;
                $countTypes = str_repeat('i', count($allUnitIds));

                if (!empty($search)) {
                    $countParams = array_merge($countParams, $searchParams);
                    $countTypes .= $searchTypes;
                }

                $countStmt->bind_param($countTypes, ...$countParams);

                if (!$countStmt->execute()) {
                    return [
                        'status' => 0,
                        'message' => '查询用户总数失败'
                    ];
                }

                $countResult = $countStmt->get_result();
                $totalCount = $countResult->fetch_assoc()['total'];

                // 查询分页数据
                $userSql = "SELECT id, name, id_number, phone, alt_phone_1, alt_phone_2, short_code, landline
                           FROM 3_user
                           WHERE organization_unit IN ($placeholders)" . $searchCondition . "
                           ORDER BY id ASC
                           LIMIT ? OFFSET ?";
                $userStmt = $conn->prepare($userSql);

                if (!$userStmt) {
                    return [
                        'status' => 0,
                        'message' => '数据库准备失败: ' . $conn->error
                    ];
                }

                // 绑定参数：单位IDs + 搜索参数 + LIMIT + OFFSET
                $allParams = $allUnitIds;
                $allTypes = str_repeat('i', count($allUnitIds));

                if (!empty($search)) {
                    $allParams = array_merge($allParams, $searchParams);
                    $allTypes .= $searchTypes;
                }

                $allParams = array_merge($allParams, [$pageSize, $offset]);
                $allTypes .= 'ii';

                $userStmt->bind_param($allTypes, ...$allParams);
            } else {
                return [
                    'status' => 0,
                    'message' => '未找到相关单位'
                ];
            }
        }

        if (!$userStmt->execute()) {
            return [
                'status' => 0,
                'message' => '查询用户信息失败'
            ];
        }

        $userResult = $userStmt->get_result();
        $users = [];
        while ($row = $userResult->fetch_assoc()) {
            // 对身份证号进行脱敏处理：显示前十位，后面全部用*号
            if (!empty($row['id_number'])) {
                $idNumber = $row['id_number'];
                if (strlen($idNumber) > 10) {
                    $start = substr($idNumber, 0, 10);
                    $asterisks = str_repeat('*', strlen($idNumber) - 10);
                    $row['id_number'] = $start . $asterisks;
                }
            }
            $users[] = $row;
        }

        // 计算分页信息
        $totalPages = ceil($totalCount / $pageSize);

        return [
            'status' => 1,
            'message' => '查询成功',
            'data' => $users,
            'unitId' => $unitId,
            'pagination' => [
                'currentPage' => $page,
                'pageSize' => $pageSize,
                'totalCount' => $totalCount,
                'totalPages' => $totalPages,
                'hasNextPage' => $page < $totalPages,
                'hasPrevPage' => $page > 1
            ]
        ];

    } catch (Exception $e) {
        error_log("获取用户信息异常: " . $e->getMessage());
        return [
            'status' => 0,
            'message' => '系统异常，请稍后重试'
        ];
    }
}

// 编辑用户联系方式的函数
function editUser() {
    global $conn;

    $userId = $_POST['id'] ?? '';
    $phone = $_POST['phone'] ?? '';
    $alt_phone_1 = $_POST['alt_phone_1'] ?? '';
    $alt_phone_2 = $_POST['alt_phone_2'] ?? '';
    $short_code = $_POST['short_code'] ?? '';
    $landline = $_POST['landline'] ?? '';

    if (empty($userId) || !is_numeric($userId)) {
        return [
            'status' => 0,
            'message' => '用户ID参数无效'
        ];
    }

    try {
        // 验证用户是否存在
        $checkSql = "SELECT id FROM 3_user WHERE id = ?";
        $checkStmt = $conn->prepare($checkSql);

        if (!$checkStmt) {
            return [
                'status' => 0,
                'message' => '数据库准备失败: ' . $conn->error
            ];
        }

        $checkStmt->bind_param('i', $userId);
        if (!$checkStmt->execute()) {
            return [
                'status' => 0,
                'message' => '查询用户失败'
            ];
        }

        $checkResult = $checkStmt->get_result();
        if ($checkResult->num_rows === 0) {
            return [
                'status' => 0,
                'message' => '用户不存在'
            ];
        }

        // 更新用户联系方式
        $updateSql = "UPDATE 3_user SET phone = ?, alt_phone_1 = ?, alt_phone_2 = ?, short_code = ?, landline = ? WHERE id = ?";
        $updateStmt = $conn->prepare($updateSql);

        if (!$updateStmt) {
            return [
                'status' => 0,
                'message' => '数据库准备失败: ' . $conn->error
            ];
        }

        $updateStmt->bind_param('sssssi', $phone, $alt_phone_1, $alt_phone_2, $short_code, $landline, $userId);

        if (!$updateStmt->execute()) {
            return [
                'status' => 0,
                'message' => '更新用户信息失败'
            ];
        }

        $affectedRows = $updateStmt->affected_rows;

        if ($affectedRows > 0) {
            return [
                'status' => 1,
                'message' => '用户联系方式更新成功',
                'data' => [
                    'userId' => $userId,
                    'updatedFields' => [
                        'phone' => $phone,
                        'alt_phone_1' => $alt_phone_1,
                        'alt_phone_2' => $alt_phone_2,
                        'short_code' => $short_code,
                        'landline' => $landline
                    ]
                ]
            ];
        } else {
            return [
                'status' => 1,
                'message' => '用户联系方式无变化',
                'data' => [
                    'userId' => $userId
                ]
            ];
        }

    } catch (Exception $e) {
        error_log("编辑用户信息异常: " . $e->getMessage());
        return [
            'status' => 0,
            'message' => '系统异常，请稍后重试'
        ];
    }
}

// 获取单位信息的函数
function getUnits() {
    global $conn;

    $userId = $_SESSION['user_id'];

    try {
        // 查询用户角色信息
        $userRoleSql = "SELECT roleId, unitId, appId FROM 3_user_Role WHERE userId = ?";
        $userRoleStmt = $conn->prepare($userRoleSql);

        if (!$userRoleStmt) {
            return [
                'status' => 0,
                'message' => '数据库准备失败: ' . $conn->error
            ];
        }

        $userRoleStmt->bind_param('i', $userId);
        if (!$userRoleStmt->execute()) {
            return [
                'status' => 0,
                'message' => '查询用户角色失败'
            ];
        }

        $userRoleResult = $userRoleStmt->get_result();

        if ($userRoleResult->num_rows === 0) {
            return [
                'status' => 0,
                'message' => '权限不足'
            ];
        }

        // 获取用户所有角色
        $userRoles = [];
        while ($roleRow = $userRoleResult->fetch_assoc()) {
            $userRoles[] = $roleRow;
        }

        // 检查是否是系统管理员 (roleId = 1)
        $isSystemAdmin = false;
        $authorizedUnits = [];

        foreach ($userRoles as $role) {
            if ($role['roleId'] == 1) {
                $isSystemAdmin = true;
                break;
            }
            // 收集部门管理员(3)的授权单位
            if ($role['roleId'] == 3) {
                $authorizedUnits[] = $role['unitId'];
            }
        }

        if ($isSystemAdmin) {
            // 系统管理员：返回所有单位
            $unitSql = "SELECT id, unit_name, code, parent_id, sort_order FROM 2_unit ORDER BY sort_order ASC";
            $unitStmt = $conn->prepare($unitSql);

            if (!$unitStmt) {
                return [
                    'status' => 0,
                    'message' => '数据库准备失败: ' . $conn->error
                ];
            }

            if (!$unitStmt->execute()) {
                return [
                    'status' => 0,
                    'message' => '查询单位信息失败'
                ];
            }

            $unitResult = $unitStmt->get_result();
            $units = [];
            while ($row = $unitResult->fetch_assoc()) {
                $units[] = $row;
            }

            // 构建树状结构
            $tree = buildTree($units);

            return [
                'status' => 1,
                'message' => '查询成功',
                'data' => $tree,
                'userType' => 'system_admin'
            ];

        } else if (!empty($authorizedUnits)) {
            // 部门管理员：返回授权的单位和子单位
            $allAuthorizedUnits = $authorizedUnits;

            // 获取所有授权单位的子单位
            foreach ($authorizedUnits as $unitId) {
                getAllChildUnits($conn, $unitId, $allAuthorizedUnits);
            }

            // 去重
            $allAuthorizedUnits = array_unique($allAuthorizedUnits);

            if (!empty($allAuthorizedUnits)) {
                $placeholders = implode(',', array_fill(0, count($allAuthorizedUnits), '?'));
                $unitSql = "SELECT id, unit_name, code, parent_id, sort_order FROM 2_unit WHERE id IN ($placeholders) ORDER BY sort_order ASC";
                $unitStmt = $conn->prepare($unitSql);

                if (!$unitStmt) {
                    return [
                        'status' => 0,
                        'message' => '数据库准备失败: ' . $conn->error
                    ];
                }

                $types = str_repeat('i', count($allAuthorizedUnits));
                $unitStmt->bind_param($types, ...$allAuthorizedUnits);

                if (!$unitStmt->execute()) {
                    return [
                        'status' => 0,
                        'message' => '查询单位信息失败'
                    ];
                }

                $unitResult = $unitStmt->get_result();
                $units = [];
                while ($row = $unitResult->fetch_assoc()) {
                    $units[] = $row;
                }

                // 构建以授权单位为根节点的树状结构
                $tree = [];
                foreach ($authorizedUnits as $rootUnitId) {
                    // 找到授权单位作为根节点
                    foreach ($units as $unit) {
                        if ($unit['id'] == $rootUnitId) {
                            // 为这个授权单位构建子树
                            $unitTree = $unit;
                            $unitTree['children'] = buildTree($units, $rootUnitId);
                            $tree[] = $unitTree;
                            break;
                        }
                    }
                }

                return [
                    'status' => 1,
                    'message' => '查询成功',
                    'data' => $tree,
                    'userType' => 'unit_admin',
                    'authorizedUnits' => $authorizedUnits
                ];
            } else {
                return [
                    'status' => 0,
                    'message' => '用户没有授权的单位'
                ];
            }
        } else {
            return [
                'status' => 0,
                'message' => '权限不足'
            ];
        }

    } catch (Exception $e) {
        // 捕获异常并记录日志
        error_log("获取单位信息异常: " . $e->getMessage());
        return [
            'status' => 0,
            'message' => '系统异常，请稍后重试'
        ];
    }
}

// 处理控制码
try {
    $controlCode = $_POST['controlCode'] ?? $_GET['controlCode'] ?? '';

    switch ($controlCode) {
        case 'get_units':
            $result = getUnits();
            echo json_encode($result);
            break;

        case 'get_user':
            $result = getUsers();
            echo json_encode($result);
            break;

        case 'edit_user':
            $result = editUser();
            echo json_encode($result);
            break;

        default:
            echo json_encode([
                'status' => 0,
                'message' => '无效的控制码'
            ]);
            break;
    }

} catch (Exception $e) {
    error_log("address_book_management.php Error: " . $e->getMessage());
    echo json_encode([
        'status' => 0,
        'message' => '操作失败: ' . $e->getMessage()
    ]);
} finally {
    // 关闭数据库连接
    if (isset($conn)) {
        $conn->close();
    }
}
?>

import { createRouter, createWebHashHistory  } from 'vue-router';
import UnitManagement from '@/app/views/UnitManagement.vue'; 
import UserManagement from '@/app/views/UserManagement.vue'; 
import AppManagement from '@/app/views/AppManagement.vue';
import RoleManagement from '@/app/views/RoleManagement.vue'; // 引入 Element Plus 核心样式
import DutyManagement from '@/app/views/DutyManagement.vue';
import AuthManagement from '@/app/views/AuthManagement.vue';
import NewVisualScreen1 from '@/app/views/NewVisualScreen1.vue';
import PersonTypeManagement from '@/app/views/PersonTypeManagement.vue';
import PersonInfoManagement from '@/app/views/PersonInfoManagement.vue';
import PersonStatusManagement from '@/app/views/PersonStatusManagement.vue';
import PersonMovementManagement from '@/app/views/PersonMovementManagement.vue';
import CaseHandlingManagement from '@/app/views/CaseHandlingManagement.vue';
import CaseInfoManagement from '@/app/views/CaseInfoManagement.vue';
import NewVisualScreen2 from '@/app/views/NewVisualScreen2.vue';
import NewVisualScreen3 from '@/app/views/NewVisualScreen3.vue';

const routes = [
  {
    path: '/unit-management',
    name: 'UnitManagement',
    component: UnitManagement
  },
  {
    path: '/user-management',
    name: 'UserManagement',
    component: UserManagement
  },
  {
    path: '/app-management',
    name: 'AppManagement',
    component: AppManagement
  },
  {
    path: '/role-management',
    name: 'RoleManagement',
    component: RoleManagement
  },
  {
    path: '/duty-management',
    name: 'DutyManagement',
    component: DutyManagement
  },
  {
    path: '/auth-management',
    name: 'AuthManagement',
    component: AuthManagement
  },
  {
    path: '/new-visual-screen1',
    name: 'NewVisualScreen1',
    component: NewVisualScreen1
  },
  {
    path: '/person-type-management',
    name: 'PersonTypeManagement',
    component: PersonTypeManagement
  },
  {
    path: '/person-info-management',
    name: 'PersonInfoManagement',
    component: PersonInfoManagement
  },
  {
    path: '/person-status-management',
    name: 'PersonStatusManagement',
    component: PersonStatusManagement
  },
  {
    path: '/person-movement-management',
    name: 'PersonMovementManagement',
    component: PersonMovementManagement
  },
  {
    path: '/case-handling-management',
    name: 'CaseHandlingManagement',
    component: CaseHandlingManagement
  },
  {
    path: '/case-info-management',
    name: 'CaseInfoManagement',
    component: CaseInfoManagement
  },
  {
    path: '/new-visual-screen2',
    name: 'NewVisualScreen2',
    component: NewVisualScreen2
  },
  {
    path: '/new-visual-screen3',
    name: 'NewVisualScreen3',
    component: NewVisualScreen3
  }

];

const router = createRouter({
  history: createWebHashHistory (),
  routes
});

export default router;